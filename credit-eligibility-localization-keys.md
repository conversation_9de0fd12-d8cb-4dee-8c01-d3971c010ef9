# Credit Eligibility Modal Localization Keys

## Updated Keys for Privacy Policy Consent

Based on the Figma design requirement to separate the privacy policy text and link, the following keys have been updated:

### Terminal Namespace Keys

| Key | EN Translation | Description |
|-----|----------------|-------------|
| `credit-eligibility.privacy-policy-consent` | I agree to the processing of my personal data in accordance with the | Main text for privacy policy consent (without the link) |
| `credit-eligibility.privacy-policy-link` | privacy policy | Text for the privacy policy link |

### Implementation Details

The privacy policy consent checkbox now uses two separate keys:
1. `credit-eligibility.privacy-policy-consent` - Contains the main consent text
2. `credit-eligibility.privacy-policy-link` - Contains the clickable link text

The link points to the privacy policy URL configured in the environment variables (`REACT_APP_PRIVACY_POLICY_URL`).

### Example Usage

The implementation renders as:
```
☐ I agree to the processing of my personal data in accordance with the [privacy policy]
```

Where "privacy policy" is a clickable link that opens in a new tab.

### Files Modified

1. `src/shared/constants/localization-keys/terminal.ts` - Added new key for privacy policy link
2. `src/modules/applications/shared/components/credit-eligibility-modal/CreditEligibilityForm.tsx` - Updated to use separate keys and render link component

### Notes

- The privacy policy link opens in a new tab with `target="_blank"` and `rel="noopener noreferrer"` for security
- The link styling inherits the text color and adds an underline decoration
- The implementation follows the existing pattern used in other components in the codebase
