import { But<PERSON>, Checkbox, HStack, Text, VStack } from '@chakra-ui/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Controller, useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { ColorSchemes } from 'shared/chakra-theme/foundations/colors';
import { NumericNumberInput, TextInput } from 'shared/components';
import {
  LocizeNamespaces,
  LocizeTerminalKeys,
} from 'shared/constants/localization-keys';

import { CreditEligibilityResult } from './CreditEligibilityResult';
import {
  type CreditEligibilityCheckResponse,
  type CreditEligibilityFormData,
  CreditEligibilityFormSchema,
} from './types';

interface CreditEligibilityFormProps {
  onSubmit: (data: CreditEligibilityFormData) => Promise<void>;
  isLoading: boolean;
  result?: CreditEligibilityCheckResponse;
  onClose: () => void;
}

export const CreditEligibilityForm = ({
  onSubmit,
  isLoading,
  result,
  onClose,
}: CreditEligibilityFormProps) => {
  const { t } = useTranslation([
    LocizeNamespaces.TERMINAL,
    LocizeNamespaces.COMMON,
  ]);

  const {
    control,
    register,
    handleSubmit,
    formState: { errors, isValid, isDirty },
  } = useForm<CreditEligibilityFormData>({
    resolver: zodResolver(CreditEligibilityFormSchema),
    mode: 'onChange',
    defaultValues: {
      amount: 0,
      idCode: '',
      email: '',
      privacyPolicyConsent: false,
      newsletterConsent: false,
    },
  });

  // Reset result when form fields change
  const hasFormChanged = result && isDirty;

  const handleFormSubmit = async (data: CreditEligibilityFormData) => {
    await onSubmit(data);
  };

  const isSubmitDisabled = !isValid || isLoading;

  return (
    <VStack spacing={6} align="stretch">
      <VStack spacing={4} align="stretch">
        <Controller
          name="amount"
          control={control}
          render={({ field }) => (
            <NumericNumberInput
              label={t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_AMOUNT)}
              placeholder="0"
              suffix=" €"
              error={errors.amount?.message}
              disabled={isLoading}
              {...field}
              onChange={(value) => field.onChange(value || 0)}
            />
          )}
        />

        <TextInput
          label={t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_ID_CODE)}
          placeholder={t(
            LocizeTerminalKeys.CREDIT_ELIGIBILITY_ID_CODE_PLACEHOLDER,
          )}
          error={errors.idCode?.message}
          isDisabled={isLoading}
          {...register('idCode')}
        />

        <TextInput
          label={t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_EMAIL)}
          placeholder={t(
            LocizeTerminalKeys.CREDIT_ELIGIBILITY_EMAIL_PLACEHOLDER,
          )}
          error={errors.email?.message}
          isDisabled={isLoading}
          {...register('email')}
        />
      </VStack>

      <VStack spacing={4} align="stretch" pt={2}>
        <Controller
          name="privacyPolicyConsent"
          control={control}
          render={({ field }) => (
            <Checkbox
              isChecked={field.value}
              onChange={field.onChange}
              isDisabled={isLoading}
              colorScheme={ColorSchemes.PRIMARY}
              size="lg"
            >
              <Text fontSize="14px" lineHeight="1.43">
                {t(
                  LocizeTerminalKeys.CREDIT_ELIGIBILITY_PRIVACY_POLICY_CONSENT,
                )}{' '}
                <a
                  href={process.env.REACT_APP_PRIVACY_POLICY_URL}
                  target="_blank"
                  rel="noopener noreferrer"
                  style={{
                    color: 'inherit',
                    textDecoration: 'underline',
                  }}
                >
                  {t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_PRIVACY_POLICY_LINK)}
                </a>
              </Text>
            </Checkbox>
          )}
        />

        <Controller
          name="newsletterConsent"
          control={control}
          render={({ field }) => (
            <Checkbox
              isChecked={field.value}
              onChange={field.onChange}
              isDisabled={isLoading}
              colorScheme={ColorSchemes.PRIMARY}
              size="lg"
            >
              <Text fontSize="14px" lineHeight="1.43">
                {t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_NEWSLETTER_CONSENT)}
              </Text>
            </Checkbox>
          )}
        />
      </VStack>

      {result && !hasFormChanged && <CreditEligibilityResult result={result} />}

      <HStack
        spacing={3}
        justify="flex-end"
        pt={6}
        borderTop="1px solid"
        borderColor="neutral.150"
      >
        <Button
          variant="ghost"
          colorScheme={ColorSchemes.NEUTRAL}
          onClick={onClose}
          isDisabled={isLoading}
        >
          {t('common:modal.close')}
        </Button>
        <Button
          colorScheme={ColorSchemes.PRIMARY}
          onClick={handleSubmit(handleFormSubmit)}
          isLoading={isLoading}
          isDisabled={isSubmitDisabled}
          loadingText={t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_CHECKING)}
        >
          {t(LocizeTerminalKeys.CREDIT_ELIGIBILITY_CHECK_ELIGIBILITY)}
        </Button>
      </HStack>
    </VStack>
  );
};
